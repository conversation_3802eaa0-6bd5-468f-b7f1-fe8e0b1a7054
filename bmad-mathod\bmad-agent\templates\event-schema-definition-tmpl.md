# Event Schema Definition: {Event Name}
## Event-Driven Architecture Communication Contract

### Document Information
- **Event Name:** {EventName}
- **Event Type:** {EventType} (e.g., UserCreated, OrderProcessed, PaymentCompleted)
- **Schema Version:** 1.0
- **Publishing Service:** {Service Name}
- **Creation Date:** {Date}
- **Owner Team:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Event Overview

### Business Purpose
{Clear description of what business event this represents and why it's published}

### Event Category
- [ ] **Domain Event** - Represents something that happened in the business domain
- [ ] **Integration Event** - Facilitates communication between bounded contexts
- [ ] **System Event** - Technical event for system operations
- [ ] **Notification Event** - Informational event for notifications

### Event Trigger
{What business action or system condition triggers this event}

### Expected Consumers
{List of services or systems that are expected to consume this event}

---

## 2. Event Schema Specification

### Core Event Structure
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "title": "{EventName}",
  "description": "{Event description}",
  "required": [
    "event_id",
    "event_type",
    "event_version",
    "timestamp",
    "source_service",
    "correlation_id",
    "data"
  ],
  "properties": {
    "event_id": {
      "type": "string",
      "format": "uuid",
      "description": "Unique identifier for this event instance"
    },
    "event_type": {
      "type": "string",
      "const": "{EventType}",
      "description": "Type of event"
    },
    "event_version": {
      "type": "string",
      "pattern": "^\\d+\\.\\d+$",
      "description": "Schema version (semantic versioning)"
    },
    "timestamp": {
      "type": "string",
      "format": "date-time",
      "description": "When the event occurred (ISO 8601)"
    },
    "source_service": {
      "type": "string",
      "description": "Service that published this event"
    },
    "correlation_id": {
      "type": "string",
      "format": "uuid",
      "description": "ID to correlate related events across services"
    },
    "causation_id": {
      "type": "string",
      "format": "uuid",
      "description": "ID of the command/event that caused this event"
    },
    "data": {
      "type": "object",
      "description": "Event-specific payload",
      "properties": {
        // Event-specific data schema defined below
      }
    },
    "metadata": {
      "type": "object",
      "description": "Additional context information",
      "properties": {
        "tenant_id": {
          "type": "string",
          "description": "Multi-tenant identifier"
        },
        "user_id": {
          "type": "string",
          "description": "User who triggered the event"
        },
        "session_id": {
          "type": "string",
          "description": "User session identifier"
        },
        "trace_id": {
          "type": "string",
          "description": "Distributed tracing identifier"
        }
      }
    }
  }
}
```

### Event-Specific Data Schema
```json
// Define the specific "data" payload for this event type
{
  "data": {
    "type": "object",
    "required": [
      // List required fields
    ],
    "properties": {
      "entity_id": {
        "type": "string",
        "description": "ID of the main entity this event relates to"
      },
      "entity_type": {
        "type": "string",
        "description": "Type of entity (User, Order, Product, etc.)"
      },
      "change_type": {
        "type": "string",
        "enum": ["created", "updated", "deleted", "status_changed"],
        "description": "Type of change that occurred"
      },
      "previous_state": {
        "type": "object",
        "description": "Previous state of the entity (for updates)"
      },
      "current_state": {
        "type": "object",
        "description": "Current state of the entity"
      },
      // Add event-specific fields here
      "custom_field_1": {
        "type": "string",
        "description": "Description of custom field"
      },
      "custom_field_2": {
        "type": "integer",
        "description": "Description of custom field"
      }
    }
  }
}
```

---

## 3. Event Examples

### Example 1: Successful Event
```json
{
  "event_id": "123e4567-e89b-12d3-a456-426614174000",
  "event_type": "{EventType}",
  "event_version": "1.0",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source_service": "{ServiceName}",
  "correlation_id": "987fcdeb-51a2-43d1-b123-456789abcdef",
  "causation_id": "456e7890-e12b-34d5-a678-901234567890",
  "data": {
    "entity_id": "user_12345",
    "entity_type": "User",
    "change_type": "created",
    "current_state": {
      "id": "user_12345",
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2024-01-15T10:30:00.000Z"
    },
    "custom_field_1": "example_value",
    "custom_field_2": 42
  },
  "metadata": {
    "tenant_id": "tenant_001",
    "user_id": "admin_user",
    "session_id": "session_abc123",
    "trace_id": "trace_xyz789"
  }
}
```

### Example 2: Update Event
```json
{
  "event_id": "234f5678-f90c-23e4-b567-537725285111",
  "event_type": "{EventType}",
  "event_version": "1.0",
  "timestamp": "2024-01-15T11:45:00.000Z",
  "source_service": "{ServiceName}",
  "correlation_id": "987fcdeb-51a2-43d1-b123-456789abcdef",
  "causation_id": "567f8901-f23c-45e6-b789-012345678901",
  "data": {
    "entity_id": "user_12345",
    "entity_type": "User",
    "change_type": "updated",
    "previous_state": {
      "status": "active",
      "email": "<EMAIL>"
    },
    "current_state": {
      "status": "suspended",
      "email": "<EMAIL>",
      "suspended_at": "2024-01-15T11:45:00.000Z"
    }
  },
  "metadata": {
    "tenant_id": "tenant_001",
    "user_id": "admin_user",
    "session_id": "session_def456",
    "trace_id": "trace_uvw012"
  }
}
```

---

## 4. Publishing Configuration

### Message Broker Configuration
- **Topic/Queue Name:** {topic_name}
- **Partition Strategy:** {How messages are partitioned}
- **Retention Policy:** {How long messages are retained}
- **Replication Factor:** {Number of replicas for reliability}

### Publishing Guarantees
- [ ] **At-Least-Once** - Message delivered at least once (may have duplicates)
- [ ] **At-Most-Once** - Message delivered at most once (may be lost)
- [ ] **Exactly-Once** - Message delivered exactly once (requires special handling)

### Ordering Requirements
- [ ] **No Ordering Required** - Messages can be processed in any order
- [ ] **Partition Ordering** - Messages within same partition are ordered
- [ ] **Global Ordering** - All messages must be processed in order

### Publishing Frequency
- **Expected Volume:** {Events per second/minute/hour}
- **Peak Volume:** {Maximum expected events during peak times}
- **Batch Size:** {Number of events published together}

---

## 5. Consumer Guidelines

### Processing Requirements
- **Idempotency:** Consumers must handle duplicate events gracefully
- **Error Handling:** Failed events should be retried with exponential backoff
- **Dead Letter Queue:** Failed events after max retries go to DLQ
- **Processing Timeout:** Maximum time to process an event

### Consumer Registration
{How consumers register to receive this event type}

### Filtering Options
{Available filtering options for consumers (if any)}

### Acknowledgment Requirements
{How consumers should acknowledge successful processing}

---

## 6. Backward Compatibility

### Schema Evolution Rules
- **Additive Changes:** New optional fields can be added
- **Field Removal:** Fields cannot be removed (mark as deprecated)
- **Type Changes:** Field types cannot be changed
- **Required Fields:** New required fields break compatibility

### Versioning Strategy
- **Major Version:** Breaking changes require major version bump
- **Minor Version:** Backward-compatible additions
- **Patch Version:** Bug fixes and clarifications

### Deprecation Policy
- **Deprecation Notice:** 6 months advance notice for breaking changes
- **Support Period:** Old versions supported for 12 months
- **Migration Guide:** Provided for all breaking changes

---

## 7. Monitoring and Observability

### Key Metrics
- **Publishing Rate:** Events published per second
- **Processing Latency:** Time from publish to consumption
- **Error Rate:** Percentage of failed event processing
- **Consumer Lag:** How far behind consumers are

### Alerting Rules
- **High Error Rate:** Alert when error rate > {threshold}%
- **High Latency:** Alert when processing time > {threshold}ms
- **Consumer Lag:** Alert when lag > {threshold} messages
- **Publishing Failures:** Alert on publishing failures

### Distributed Tracing
- **Trace Propagation:** How trace context is included in events
- **Span Creation:** What operations create spans
- **Correlation:** How events are correlated across services

---

## 8. Security and Privacy

### Data Classification
- [ ] **Public** - No sensitive information
- [ ] **Internal** - Company internal information
- [ ] **Confidential** - Sensitive business information
- [ ] **Restricted** - Highly sensitive or regulated data

### PII Handling
{How personally identifiable information is handled in events}

### Encryption Requirements
- [ ] **Encryption in Transit** - Events encrypted during transmission
- [ ] **Encryption at Rest** - Events encrypted in storage
- [ ] **Field-Level Encryption** - Specific fields encrypted

### Access Control
{Who can publish and consume this event type}

---

## 9. Testing Strategy

### Schema Validation Testing
- **Valid Schema Tests:** Verify events conform to schema
- **Invalid Schema Tests:** Verify invalid events are rejected
- **Evolution Tests:** Test backward compatibility

### Integration Testing
- **End-to-End Tests:** Full publish-consume workflow
- **Consumer Tests:** Verify all consumers handle events correctly
- **Failure Tests:** Test error handling and recovery

### Performance Testing
- **Load Tests:** Verify performance under expected load
- **Stress Tests:** Test behavior under extreme load
- **Latency Tests:** Measure end-to-end processing time

---

## 10. Documentation and Support

### Consumer Documentation
{Link to documentation for event consumers}

### Schema Registry
{Link to schema registry where this schema is stored}

### Support Contacts
- **Technical Owner:** {Name and contact}
- **Business Owner:** {Name and contact}
- **On-Call Support:** {Contact information}

### Related Events
{List of related events that consumers might also be interested in}

---

## 11. Approval and Sign-off

### Technical Review
- **Schema Designer:** {Name} - {Date} - {Signature}
- **Publishing Service Team:** {Name} - {Date} - {Signature}
- **Architecture Review:** {Name} - {Date} - {Signature}

### Business Review
- **Product Owner:** {Name} - {Date} - {Signature}
- **Domain Expert:** {Name} - {Date} - {Signature}

---

## 12. Change Log

| Version | Date | Changes | Author | Approver |
|---------|------|---------|--------|----------|
| 1.0 | {Date} | Initial schema definition | {Author} | {Approver} |

---

## 13. References

- **Event Storming Session:** {Link to session notes}
- **Domain Model:** {Link to domain model documentation}
- **API Documentation:** {Link to related API docs}
- **Architecture Decision Records:** {Link to relevant ADRs}
