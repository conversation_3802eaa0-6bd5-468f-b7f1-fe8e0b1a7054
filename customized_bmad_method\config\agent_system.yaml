
# Customized BMAD Agent System Configuration
# Microservices Architecture with Agentic AI Integration

system:
  name: "BMAD-Microservices-AI"
  version: "2.0.0"
  description: "Enhanced BMAD methodology for microservices architecture with agentic AI integration"
  
agents:
  analyst:
    name: "Enhanced Analyst Agent"
    personas: ["<PERSON>", "<PERSON>"]
    specializations:
      - "Domain-Driven Design Analysis"
      - "Microservices Decomposition"
      - "AI Integration Strategy"
      - "Platform Requirements Assessment"
    modes:
      - "Service Discovery Mode"
      - "AI Capability Mapping"
      - "Technology Stack Analysis"
      - "Team Topology Planning"
    
  product_manager:
    name: "Enhanced Product Manager Agent"
    personas: ["<PERSON>", "<PERSON>"]
    specializations:
      - "System-Level PRD Creation"
      - "Service-Level PRD Generation"
      - "Cross-Service Coordination"
      - "AI Workflow Integration"
    templates:
      - "Master Project PRD"
      - "Individual Service PRD"
      - "Service Dependency Matrix"
      - "AI Agent Integration Plan"
    
  platform_architect:
    name: "Platform Architect Agent"
    personas: ["Alex", "Platform"]
    specializations:
      - "Internal Developer Platform Design"
      - "Infrastructure Architecture"
      - "Developer Experience Optimization"
      - "Platform Team Coordination"
    outputs:
      - "Platform Architecture Document"
      - "Infrastructure Requirements"
      - "Developer Experience Plan"
      - "Platform Roadmap"
    
  design_architect:
    name: "Enhanced Design Architect Agent"
    personas: ["<PERSON>", "Millie"]
    specializations:
      - "Micro-Frontend Architecture"
      - "Design System Governance"
      - "AI-Enhanced UX"
      - "Multi-Modal Interface Design"
    
  ai_orchestrator:
    name: "AI Orchestration Agent"
    personas: ["Sage", "Orchestrator"]
    specializations:
      - "Multi-Agent Workflow Design"
      - "Human-AI Handoff Procedures"
      - "AI Infrastructure Planning"
      - "AI Governance Framework"

workflows:
  master_project:
    phases:
      - "System Analysis"
      - "Architecture Design"
      - "Service Decomposition"
      - "AI Integration Planning"
      - "Platform Engineering"
      - "Implementation Strategy"
    
  individual_service:
    phases:
      - "Service Analysis"
      - "Requirements Definition"
      - "API Design"
      - "Integration Planning"
      - "Implementation Specification"

templates:
  briefs:
    - "master_project_brief.md"
    - "individual_service_brief.md"
  prds:
    - "master_project_prd.md"
    - "individual_service_prd.md"
  specialized:
    - "platform_architecture.md"
    - "ai_orchestration_plan.md"
    - "service_dependency_matrix.md"
