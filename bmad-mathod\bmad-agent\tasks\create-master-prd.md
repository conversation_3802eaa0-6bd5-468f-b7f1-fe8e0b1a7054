# Master PRD Generation Task
## System-Level Product Requirements for Microservices Architecture

## Purpose

- Transform Master Project Brief into comprehensive system-level product definition documents
- Define clear system-wide scope and cross-cutting concerns for microservices architecture
- Provide foundation for Platform Architect, AI Orchestration Agent, and Individual Service PRDs
- Coordinate dependencies and integration patterns across the entire system

Remember as you follow the upcoming instructions:

- Your documents form the foundation for the entire distributed system development process
- Output will be used by Platform Architect for infrastructure design and AI Orchestration Agent for agent workflow design
- Your system-level requirements will guide individual service PRD creation
- Focus on system-wide "what" while leaving service-specific "how" for individual service PRDs

## Instructions

### 1. Define Project Workflow Context

Before Master PRD generation, ask the user to choose their intended workflow approach:

A. **System-First Approach (Recommended):** Create comprehensive system-level PRD first, then generate individual service PRDs based on system requirements and service catalog.

B. **Parallel Development Approach:** Create system-level PRD while simultaneously planning individual service PRDs for critical services.

C. **Iterative Approach:** Create initial system-level PRD, validate with key service PRDs, then iterate and refine both levels.

### 2. Determine Interaction Mode

Confirm with the user their preferred interaction style:
- **Incremental (Default):** Address Master PRD sections sequentially, seeking feedback on each
- **"YOLO" Mode:** Draft comprehensive Master PRD for single, larger review

### 3. Review System-Level Inputs

Review all inputs provided:
- Master Project Brief
- Service discovery analysis (if available)
- AI integration strategy (if available)
- Platform requirements analysis (if available)
- Any research or stakeholder input

### 4. Process Master PRD Sections

Work through the Master PRD sections using the `master-project-prd-tmpl` template:

#### 4A. System Context and Architecture
- **Project Context and Objectives**: System-wide business goals and strategic objectives
- **System Architecture Overview**: High-level topology and major components
- **Service Catalog Definition**: Complete inventory of planned microservices
- **Communication Patterns**: Inter-service protocols and integration strategies

#### 4B. Cross-Cutting Requirements
- **Security and Compliance Framework**: System-wide security and regulatory requirements
- **Performance and Scalability**: System-level SLAs and scaling strategies
- **Reliability and Resilience**: Fault tolerance and business continuity requirements
- **Observability and Monitoring**: Centralized logging, metrics, and alerting

#### 4C. AI Agent Ecosystem Design
- **Agent Orchestration Service**: Multi-agent workflow coordination requirements
- **Intelligence Hub Service**: Centralized analytics and predictive capabilities
- **Conversational AI Service**: Natural language processing requirements
- **Automation Engine Service**: Task automation and decision execution
- **Learning & Adaptation Service**: Continuous improvement and model evolution

#### 4D. Platform Engineering Strategy
- **Internal Developer Platform Requirements**: Self-service capabilities and golden paths
- **Infrastructure Requirements**: Kubernetes, service mesh, and cloud services
- **Developer Experience Optimization**: Tooling, automation, and productivity
- **Platform Team Responsibilities**: Platform-as-a-product approach

### 5. Epic and Service Breakdown Strategy

#### 5A. System-Level Epic Definition
Create epics that span multiple services and deliver end-to-end value:
- **Epic 1: Platform Foundation** - Infrastructure, security, core services
- **Epic 2: Core Business Services** - Primary business logic and workflows
- **Epic 3: Data Services** - Analytics, intelligence, data management
- **Epic 4: Integration Services** - External APIs and legacy connectivity
- **Epic 5: AI Agent Services** - Agentic AI capabilities and orchestration
- **Epic 6: Frontend Applications** - User interfaces and micro-frontend architecture

#### 5B. Service Dependency Matrix
Create comprehensive mapping of:
- Cross-service relationships and communication patterns
- API contract specifications and integration requirements
- Event-driven architecture and message schemas
- External integrations and third-party dependencies

### 6. AI Integration and Human-AI Collaboration

#### 6A. Multi-Agent Workflow Design
- **Agent Coordination Patterns**: How AI agents collaborate across services
- **Task Distribution Logic**: Intelligent routing based on agent capabilities
- **Human-AI Handoff Procedures**: Escalation protocols and collaboration patterns
- **Context Preservation**: Memory and state management across agent interactions

#### 6B. AI Infrastructure Planning
- **Vector Database Strategy**: Embedding storage and semantic search requirements
- **Model Serving Architecture**: Inference scaling and model management
- **AI Observability**: Monitoring, evaluation, and performance tracking
- **AI Governance Framework**: Ethics, compliance, and quality assurance

### 7. Platform and Infrastructure Requirements

#### 7A. Internal Developer Platform Design
- **Self-Service Capabilities**: Golden paths and developer portal requirements
- **Platform Service Catalog**: Available services and their interfaces
- **Developer Experience**: Tooling, automation, and productivity optimization
- **Platform Team Topology**: Team structure and responsibility definition

#### 7B. Infrastructure Architecture
- **Kubernetes Strategy**: Cluster design and resource management
- **Service Mesh Planning**: Istio/Linkerd implementation requirements
- **Networking and Security**: Zero Trust architecture and communication patterns
- **Monitoring and Observability**: Centralized logging, metrics, and tracing

### 8. Implementation Timeline and Coordination

#### 8A. Phased Delivery Strategy
- **Phase 1: Foundation** - Platform setup and core infrastructure
- **Phase 2: Core Services** - Business logic and data services
- **Phase 3: AI Integration** - Agentic AI capabilities and orchestration
- **Phase 4: Advanced Features** - Advanced capabilities and optimization

#### 8B. Cross-Service Coordination
- **Dependency Management**: Service interdependencies and coordination requirements
- **Integration Testing**: Contract testing and end-to-end validation
- **Deployment Orchestration**: Coordinated deployment across services
- **Change Management**: System-wide change coordination and communication

### 9. Quality Assurance and Governance

#### 9A. System-Level Quality Gates
- **Architecture Compliance**: Validation against architectural principles
- **Security Validation**: System-wide security and compliance verification
- **Performance Testing**: End-to-end performance and scalability validation
- **Integration Testing**: Cross-service interaction and contract validation

#### 9B. Governance Framework
- **Change Management**: System-wide change approval and coordination
- **Documentation Standards**: Consistent documentation across all services
- **Quality Standards**: Code quality, testing, and operational excellence
- **Compliance Management**: Regulatory requirements and audit procedures

### 10. Handoff Instructions and Next Steps

#### 10A. Platform Architect Handoff
Create detailed prompt for Platform Architect including:
- Infrastructure requirements and constraints
- IDP capabilities and developer experience needs
- Kubernetes and service mesh requirements
- Monitoring and operational excellence requirements

#### 10B. AI Orchestration Agent Handoff
Create detailed prompt for AI Orchestration Agent including:
- Multi-agent workflow requirements
- Human-AI collaboration patterns
- AI infrastructure and scaling needs
- AI governance and quality requirements

#### 10C. Individual Service PRD Generation
Create framework for generating individual service PRDs:
- Service catalog with detailed specifications
- Cross-service dependency requirements
- Integration patterns and communication protocols
- Quality and compliance requirements for each service

### 11. Validation and Iteration

#### 11A. System Coherence Validation
- **Architecture Consistency**: Ensure all components work together coherently
- **Dependency Validation**: Verify all dependencies are properly addressed
- **Integration Completeness**: Ensure all integration points are covered
- **Quality Completeness**: Verify all quality requirements are addressed

#### 11B. Stakeholder Review and Approval
- **Technical Review**: Architecture and technical feasibility validation
- **Business Review**: Business value and strategic alignment validation
- **Operational Review**: Operational feasibility and resource requirements
- **Compliance Review**: Regulatory and governance requirements validation

## Deliverables

### Primary Deliverable
Complete Master Project PRD following the `master-project-prd-tmpl` template with:
- Comprehensive system-level requirements
- Service catalog and dependency matrix
- AI agent ecosystem specifications
- Platform engineering requirements
- Implementation timeline and coordination plan

### Secondary Deliverables
- Platform Architect prompt with infrastructure requirements
- AI Orchestration Agent prompt with workflow requirements
- Individual Service PRD generation framework
- System-level quality assurance and governance framework

## Success Criteria

- Master PRD provides clear system-wide vision and requirements
- All cross-cutting concerns are properly addressed
- Service boundaries and dependencies are clearly defined
- AI integration strategy is comprehensive and actionable
- Platform requirements enable autonomous team development
- Implementation plan is realistic and achievable
- Quality and governance frameworks ensure system coherence
