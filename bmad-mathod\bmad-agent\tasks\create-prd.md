# Microservices PRD Generation Task

## Purpose

- Transform business requirements into comprehensive product definition documents for microservices ecosystems
- Define system-wide and service-specific requirements using microservices-native templates
- Establish foundation for Platform Engineering, AI Orchestration, and Service Mesh Architecture
- Create detailed specifications for distributed systems with agentic AI integration

Remember as you follow the upcoming instructions:

- Your documents form the foundation for enterprise-scale microservices development
- Output will be used by Platform Engineers, AI Orchestration Specialists, and Service Mesh Architects
- Your specifications will guide multi-service coordination and AI agent integration
- Focus on distributed system patterns, cross-service dependencies, and platform engineering requirements
- Consider Conway's Law implications and team topology alignment in all specifications

## Instructions

### 1. Define Microservices Project Context

- Before PRD generation, determine the microservices project scope and approach:

  **Core Microservices PRD Types:**
  A. **Master System PRD (Default):** Create comprehensive system-level PRD for entire microservices ecosystem with service catalog, cross-cutting concerns, and platform engineering requirements

  B. **Individual Service PRD:** Create detailed PRD for a specific microservice within an existing ecosystem (requires existing Master PRD for context)

  C. **AI Agent Integration PRD:** Focus on agentic AI capabilities, multi-agent orchestration, and human-AI collaboration patterns

  D. **Platform Engineering PRD:** Define Internal Developer Platform requirements, developer experience optimization, and platform-as-a-product approach

  **Advanced Microservices Specifications:**
  E. **Service Integration Contract:** Define cross-service communication contracts, API specifications, and event schemas

  F. **Event-Driven Architecture Specification:** Plan event sourcing, CQRS, and distributed event patterns

- Explain this choice determines the template structure and specialized requirements focus

### 2. Determine Interaction Mode and Template Selection

- Confirm the user's preferred interaction style for creating the PRD:
  - **Incremental Mode (Default):** Address PRD sections sequentially, seeking feedback on each section. For service catalogs and epics: present the ordered list for approval, then detail each component systematically.
  - **"YOLO" Mode:** Draft comprehensive PRD with multiple sections, service specifications, and integration contracts for single, comprehensive review.

### 3. Review Microservices Context and Requirements

Review all available inputs including:
- Master Project Brief (if available)
- Service boundary analysis and domain modeling
- AI integration strategy and agent requirements
- Platform engineering needs and developer experience requirements
- Cross-service dependencies and integration patterns

### 4. Select and Process Microservices Template

Based on the selected PRD type, use the appropriate microservices-native template:

**Template Selection Logic:**
- **Master System PRD:** Use `master-project-prd-tmpl` for comprehensive ecosystem specification
- **Individual Service PRD:** Use `individual-service-prd-tmpl` with Master PRD context alignment
- **AI Agent Integration:** Use `ai-agent-integration-tmpl` for agentic AI specifications
- **Platform Engineering:** Use `platform-engineering-strategy-tmpl` for IDP requirements
- **Service Integration:** Use `service-integration-contract-tmpl` for cross-service contracts
- **Event-Driven Architecture:** Use `event-schema-definition-tmpl` for event specifications

<important_note>For all microservices PRDs, explicitly address distributed system considerations including service boundaries, communication patterns, data consistency models, and platform engineering requirements. Ensure alignment with enterprise architecture principles, Conway's Law implications, and team topology patterns. Document technology stack decisions for microservices ecosystem including container orchestration, service mesh, event streaming, and AI infrastructure.</important_note>

### 5. Process Microservices PRD Sections

Work through the selected template sections systematically, addressing microservices-specific considerations:

**For Master System PRD:**
- **System Vision and Strategy:** Define overall ecosystem purpose and business value
- **Service Catalog and Boundaries:** Identify services using domain-driven design principles
- **Cross-Cutting Concerns:** Address security, monitoring, compliance across all services
- **Platform Engineering Requirements:** Define IDP capabilities and developer experience needs
- **AI Integration Strategy:** Plan agentic AI placement and orchestration across services
- **Technology Strategy:** Establish technology stack for microservices ecosystem

**For Individual Service PRD:**
- **Service Purpose and Boundaries:** Define service responsibility and domain boundaries
- **Service Dependencies:** Identify upstream and downstream service relationships
- **API and Event Contracts:** Specify service interfaces and communication patterns
- **Data Management:** Define service data ownership and consistency requirements
- **Performance and Scaling:** Establish service-specific performance and scaling requirements

**For AI Agent Integration PRD:**
- **Agent Capabilities and Purpose:** Define AI agent functionality and business value
- **Human-AI Collaboration:** Specify handoff procedures and escalation protocols
- **Multi-Agent Orchestration:** Plan agent coordination and workflow patterns
- **Service Integration:** Define how agents integrate with microservices architecture
- **AI Governance and Ethics:** Establish AI governance and compliance frameworks

### 6. Microservices Epic and Story Generation

For microservices PRDs, organize work into service-oriented epics and cross-service coordination stories:

#### 6A. Service-Oriented Epic Strategy

**For Master System PRD:**
- **Platform Foundation Epic:** Establish IDP, CI/CD, monitoring, and security infrastructure
- **Core Service Development Epics:** One epic per major service or bounded context
- **Cross-Service Integration Epics:** Service communication, event-driven workflows, and data consistency
- **AI Integration Epics:** Agent deployment, orchestration, and human-AI collaboration
- **Operational Excellence Epics:** Monitoring, alerting, incident response, and compliance

**For Individual Service PRD:**
- **Service Foundation Epic:** Service infrastructure, deployment, and basic functionality
- **Core Business Logic Epics:** Domain-specific functionality and business rules
- **Integration Epics:** API contracts, event handling, and service dependencies
- **Performance and Scaling Epics:** Optimization, caching, and scaling capabilities

#### 6B. Cross-Service Story Coordination

**Service Dependency Management:**
- Identify upstream and downstream service dependencies
- Define integration contracts and API specifications
- Plan event-driven communication patterns
- Establish data consistency and transaction boundaries

**Platform Engineering Stories:**
- Self-service capabilities and golden paths
- Developer experience optimization
- Monitoring and observability integration
- Security and compliance automation

**AI Integration Stories:**
- Agent deployment and configuration
- Human-AI collaboration workflows
- Multi-agent orchestration patterns
- AI governance and ethics implementation

### 7. Complete Microservices PRD Draft

Present the user with the complete microservices PRD draft once all sections are completed (or as per YOLO mode interaction).

### 8. Microservices Architecture Handoff

Based on the PRD type, recommend appropriate next steps:

**For Master System PRD:**
- **Platform Engineer:** Design Internal Developer Platform and developer experience
- **Service Mesh Architect:** Plan service communication and infrastructure
- **AI Orchestration Specialist:** Design multi-agent systems and AI integration

**For Individual Service PRD:**
- **Service Architect:** Design service-specific architecture and implementation
- **Integration Specialist:** Define service contracts and communication patterns

**For AI Agent Integration PRD:**
- **AI Orchestration Specialist:** Implement agent capabilities and orchestration
- **Platform Engineer:** Integrate AI infrastructure with platform capabilities

### 9. Microservices Checklist Assessment

Use microservices-specific checklists to validate PRD completeness:
- **Service Boundary Validation:** Ensure services align with business capabilities
- **Integration Contract Completeness:** Verify all service communications are documented
- **Platform Engineering Requirements:** Validate IDP and developer experience needs
- **AI Integration Specifications:** Confirm AI agent and orchestration requirements
- **Cross-Cutting Concerns:** Ensure security, monitoring, and compliance are addressed

### 10. Produce the Microservices PRD

Produce the PRD using the selected microservices template with the following guidance:

**General Presentation & Content:**

- Present Project Briefs (drafts or final) in a clean, full format.
- Crucially, DO NOT truncate information that has not changed from a previous version.
- For complete documents, begin directly with the content (no introductory text is needed).

<important_note>
**Next Steps for UI/UX Specification (If Applicable):**

- If the product described in this PRD includes a user interface:

  1.  **Include Design Architect Prompt in PRD:** You will add a dedicated section in the PRD document you are producing, specifically at the location marked `(END Checklist START Design Architect UI/UX Specification Mode Prompt)` (as per the `prd-tmpl` structure). This section will contain a prompt for the **Design Architect** agent.

      - The prompt should clearly state that the Design Architect is to operate in its **'UI/UX Specification Mode'**.

      - It should instruct the Design Architect to use this PRD as primary input to collaboratively define and document detailed UI/UX specifications. This might involve creating/populating a `front-end-spec-tmpl` and ensuring key UI/UX considerations are integrated or referenced back into the PRD to enrich it.

      - Example prompt text to insert:

        ```markdown
        ## Prompt for Design Architect (UI/UX Specification Mode)

        **Objective:** Elaborate on the UI/UX aspects of the product defined in this PRD.
        **Mode:** UI/UX Specification Mode
        **Input:** This completed PRD document.
        **Key Tasks:**

        1. Review the product goals, user stories, and any UI-related notes herein.
        2. Collaboratively define detailed user flows, wire-frames (conceptual), and key screen mockups/descriptions.
        3. Specify usability requirements and accessibility considerations.
        4. Populate or create the `front-end-spec-tmpl` document.
        5. Ensure that this PRD is updated or clearly references the detailed UI/UX specifications derived from your work, so that it provides a comprehensive foundation for subsequent architecture and development phases.

        Please guide the user through this process to enrich the PRD with detailed UI/UX specifications.
        ```

  2.  **Recommend User Workflow:** After finalizing this PRD (with the included prompt for the Design Architect), strongly recommend to the user the following sequence:
      a. First, engage the **Design Architect** agent (using the prompt you've embedded in the PRD) to operate in **'UI/UX Specification Mode'**. Explain that this step is crucial for detailing the user interface and experience, and the output (e.g., a populated `front-end-spec-tmpl` and potentially updated PRD sections) will be vital.
      b. Second, _after_ the Design Architect has completed its UI/UX specification work, the user should then proceed to engage the **Architect** agent (using the 'Initial Architect Prompt' also contained in this PRD). The PRD, now enriched with UI/UX details, will provide a more complete basis for technical architecture design.

- If the product does not include a user interface, you will simply recommend proceeding to the Architect agent using the 'Initial Architect Prompt' in the PRD.
  </important_note>

## Guiding Principles for Epic and User Story Generation

### I. Strategic Foundation: Define Core Value & MVP Scope Rigorously

Understand & Clarify Core Needs: Start by deeply understanding and clarifying the core problem this product solves, the essential needs of the defined User Personas (or system actors), and the key business objectives for the Minimum Viable Product (MVP).
Challenge Scope Relentlessly: Actively challenge all requested features and scope at every stage. For each potential feature or story, rigorously ask, "Does this directly support the core MVP goals and provide significant value to a target User Persona?" Clearly identify and defer non-essential functionalities to a Post-MVP backlog.

### II. Structuring the Work: Value-Driven Epics & Logical Sequencing

Organize into Deployable, Value-Driven Epics: Structure the MVP scope into Epics. Each Epic must be designed to deliver a significant, end-to-end, and fully deployable increment of testable functionality that provides tangible value to the user or business. Epics should represent logical functional blocks or coherent user journeys.

Logical Epic Sequencing & Foundational Work:
Ensure the sequence of Epics follows a logical implementation order, making dependencies between Epics clear and explicitly managed.
The first Epic must always establish the foundational project infrastructure (e.g., initial app setup, Git repository, CI/CD pipeline, core cloud service configurations, basic user authentication shell if needed universally) necessary to support its own deployable functionality and that of subsequent Epics.
Ensure Logical Story Sequencing and Dependency Awareness within Epics:
After initially drafting all User Stories for an Epic, but before detailed review with the user, you (the AI Agent executing this task) must explicitly perform an internal review to establish a logical sequence for these stories.
For each story, identify if it has direct prerequisite stories within the same Epic or from already completed Epics.
Propose a clear story order to the user, explaining the rationale based on these dependencies (e.g., "Story X needs to be done before Story Y because..."). Make significant dependencies visible, perhaps as a note within the story description.

### III. Crafting Effective User Stories: Vertical Slices Focused on Value & Clarity

Define Stories as "Vertical Slices": Within each Epic, define User Stories as "vertical slices". This means each story must deliver a complete piece of functionality that achieves a specific user or system goal, potentially cutting through all necessary layers (e.g., UI, API, business logic, database).
Focus on "What" and "Why," Not "How":
Stories will primarily focus on the functional outcome, the user value ("what"), and the reason ("why"). Avoid detailing technical implementation ("how") in the story's main description.
The "As a {specific User Persona/system actor}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}" format is standard. Be precise and consistent when defining the '{specific User Persona/system actor}', ensuring it aligns with defined personas.
Ensure User Value, Not Just Technical Tasks: User Stories must articulate clear user or business value. Avoid creating stories that are purely technical tasks (e.g., "Set up database," "Refactor module X"), unless they are part of the foundational infrastructure Epic or are essential enabling tasks that are explicitly linked to, and justified by, a user-facing story that delivers value.
Appropriate Sizing & Strive for Independence:
Ensure User Stories are appropriately sized for a typical development iteration (i.e., can be completed by the team in one sprint/iteration).
If a vertically sliced story is too large or complex, work with the user to split it into smaller, still valuable, and still vertically sliced increments.
Where feasible, define stories so they can be developed, tested, and potentially delivered independently of others. If dependencies are unavoidable, they must be clearly identified and managed through sequencing.

### IV. Detailing Stories: Comprehensive Acceptance Criteria & Developer Enablement

Clear, Comprehensive, and Testable Acceptance Criteria (ACs):
Every User Story will have detailed, unambiguous, and testable Acceptance Criteria.
ACs precisely define what "done" means for that story from a functional perspective and serve as the basis for verification.
Where a specific Non-Functional Requirement (NFR) from the PRD (e.g., a particular performance target for a specific action, a security constraint for handling certain data) is critical to a story, ensure it is explicitly captured or clearly referenced within its Acceptance Criteria.
Integrate Developer Enablement & Iterative Design into Stories:
Local Testability (CLI): For User Stories involving backend processing or data components, ensure the ACs consider or specify the ability for developers to test that functionality locally (e.g., via CLI commands, local service instances).
Iterative Schema Definition: Database schema changes (new tables, columns) should be introduced iteratively within the User Stories that functionally require them, rather than defining the entire schema upfront.
Upfront UI/UX Standards (if UI applicable): For User Stories with a UI component, ACs should explicitly state requirements regarding look and feel, responsiveness, and adherence to chosen frameworks/libraries (e.g., Tailwind CSS, shadcn/ui) from the start.

### V. Managing Complexity: Addressing Cross-Cutting Concerns Effectively

Critically Evaluate for Cross-Cutting Concerns:
Before finalizing a User Story, evaluate if the described functionality is truly a discrete, user-facing piece of value or if it represents a cross-cutting concern (e.g., a specific logging requirement, a UI theme element used by many views, a core technical enabler for multiple other stories, a specific aspect of error handling).
If a piece of functionality is identified as a cross-cutting concern:
a. Avoid creating a separate User Story for it unless it delivers standalone, testable user value.
b. Instead, integrate the requirement as specific Acceptance Criteria within all relevant User Stories it impacts.
c. Alternatively, if it's a pervasive technical enabler or a non-functional requirement that applies broadly, document it clearly within the relevant PRD section (e.g., 'Non Functional Requirements', 'Technical Assumptions'), or as a note for the Architect within the story descriptions if highly specific.

Your aim is to ensure User Stories remain focused on delivering measurable user value, while still capturing all necessary technical and functional details appropriately.

### VI. Ensuring Quality & Smooth Handoff

Maintain Clarity for Handoff and Architectural Freedom: User Stories, their descriptions, and Acceptance Criteria must be detailed enough to provide the Architect with a clear and comprehensive understanding of "what is required," while allowing for architectural flexibility on the "how."
Confirm "Ready" State: Before considering an Epic's stories complete, ensure each story is effectively "ready" for subsequent architectural review or development planning – meaning it's clear, understandable, testable, its dependencies are noted, and any foundational work (like from the first epic) is accounted for.

## Offer Advanced Self-Refinement & Elicitation Options

(This section is called when needed prior to this)

Present the user with the following list of 'Advanced Reflective, Elicitation & Brainstorming Actions'. Explain that these are optional steps to help ensure quality, explore alternatives, and deepen the understanding of the current section before finalizing it and moving on. The user can select an action by number, or choose to skip this and proceed to finalize the section.

"To ensure the quality of the current section: **[Specific Section Name]** and to ensure its robustness, explore alternatives, and consider all angles, I can perform any of the following actions. Please choose a number (8 to finalize and proceed):

**Advanced Reflective, Elicitation & Brainstorming Actions I Can Take:**

{Instruction for AI Agent: Display the title of each numbered item below. If the user asks what a specific option means, provide a brief explanation of the action you will take, drawing from detailed descriptions tailored for the context.}

1.  **Critical Self-Review & User Goal Alignment**
2.  **Generate & Evaluate Alternative Design Solutions**
3.  **User Journey & Interaction Stress Test (Conceptual)**
4.  **Deep Dive into Design Assumptions & Constraints**
5.  **Usability & Accessibility Audit Review & Probing Questions**
6.  **Collaborative Ideation & UI Feature Brainstorming**
7.  **Elicit 'Unforeseen User Needs' & Future Interaction Questions**
8.  **Finalize this Section and Proceed.**

After I perform the selected action, we can discuss the outcome and decide on any further revisions for this section."

REPEAT by Asking the user if they would like to perform another Reflective, Elicitation & Brainstorming Action UNIT the user indicates it is time to proceed ot the next section (or selects #8)
